import os
import asyncio
import logging
from typing import Optional, Callable, Any
from pyrogram import Client
from pyrogram.types import Message
from pyrogram.errors import FloodWait
from bot.core.pyrogram_client import pyrogram_manager

logger = logging.getLogger(__name__)


class VideoUploader:
    """
    Handles video uploads using Pyrogram client with progress tracking and error handling.
    Designed to work alongside Telethon without event loop conflicts.
    """
    
    def __init__(self):
        self.upload_progress_callbacks = {}
    
    async def upload_video(
        self,
        chat_id: int,
        video_path: str,
        caption: str = "",
        thumbnail_path: Optional[str] = None,
        duration: int = 0,
        width: int = 0,
        height: int = 0,
        supports_streaming: bool = True,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Optional[Message]:
        """
        Upload a video file using Pyrogram client.
        
        Args:
            chat_id: Target chat ID
            video_path: Path to the video file
            caption: Video caption
            thumbnail_path: Path to thumbnail image (optional)
            duration: Video duration in seconds
            width: Video width
            height: Video height
            supports_streaming: Whether video supports streaming
            progress_callback: Callback function for upload progress
            progress_args: Additional arguments for progress callback
            
        Returns:
            Message object if successful, None otherwise
        """
        
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            return None
        
        if thumbnail_path and not os.path.exists(thumbnail_path):
            logger.warning(f"Thumbnail file not found: {thumbnail_path}")
            thumbnail_path = None
        
        try:
            # Use the pyrogram manager to execute the upload
            return await pyrogram_manager.execute_with_client(
                self._upload_video_with_client,
                chat_id=chat_id,
                video_path=video_path,
                caption=caption,
                thumbnail_path=thumbnail_path,
                duration=duration,
                width=width,
                height=height,
                supports_streaming=supports_streaming,
                progress_callback=progress_callback,
                progress_args=progress_args
            )
            
        except Exception as e:
            logger.error(f"Failed to upload video {video_path}: {e}")
            return None
    
    async def _upload_video_with_client(
        self,
        client: Client,
        chat_id: int,
        video_path: str,
        caption: str = "",
        thumbnail_path: Optional[str] = None,
        duration: int = 0,
        width: int = 0,
        height: int = 0,
        supports_streaming: bool = True,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Message:
        """Internal method to upload video with Pyrogram client"""
        
        # Prepare progress callback wrapper
        wrapped_progress = None
        if progress_callback:
            wrapped_progress = self._create_progress_wrapper(
                progress_callback, progress_args
            )
        
        # Upload the video
        message = await client.send_video(
            chat_id=chat_id,
            video=video_path,
            caption=caption,
            duration=duration,
            width=width,
            height=height,
            thumb=thumbnail_path,
            supports_streaming=supports_streaming,
            progress=wrapped_progress
        )
        
        logger.info(f"Successfully uploaded video to chat {chat_id}")
        return message
    
    def _create_progress_wrapper(self, callback: Callable, args: tuple) -> Callable:
        """Create a wrapper for progress callback to handle async execution"""
        
        async def progress_wrapper(current: int, total: int):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(current, total, *args)
                else:
                    callback(current, total, *args)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
        
        return progress_wrapper
    
    async def upload_document(
        self,
        chat_id: int,
        document_path: str,
        caption: str = "",
        thumbnail_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Optional[Message]:
        """
        Upload a document/file using Pyrogram client.
        
        Args:
            chat_id: Target chat ID
            document_path: Path to the document file
            caption: Document caption
            thumbnail_path: Path to thumbnail image (optional)
            progress_callback: Callback function for upload progress
            progress_args: Additional arguments for progress callback
            
        Returns:
            Message object if successful, None otherwise
        """
        
        if not os.path.exists(document_path):
            logger.error(f"Document file not found: {document_path}")
            return None
        
        if thumbnail_path and not os.path.exists(thumbnail_path):
            logger.warning(f"Thumbnail file not found: {thumbnail_path}")
            thumbnail_path = None
        
        try:
            return await pyrogram_manager.execute_with_client(
                self._upload_document_with_client,
                chat_id=chat_id,
                document_path=document_path,
                caption=caption,
                thumbnail_path=thumbnail_path,
                progress_callback=progress_callback,
                progress_args=progress_args
            )
            
        except Exception as e:
            logger.error(f"Failed to upload document {document_path}: {e}")
            return None
    
    async def _upload_document_with_client(
        self,
        client: Client,
        chat_id: int,
        document_path: str,
        caption: str = "",
        thumbnail_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Message:
        """Internal method to upload document with Pyrogram client"""
        
        # Prepare progress callback wrapper
        wrapped_progress = None
        if progress_callback:
            wrapped_progress = self._create_progress_wrapper(
                progress_callback, progress_args
            )
        
        # Upload the document
        message = await client.send_document(
            chat_id=chat_id,
            document=document_path,
            caption=caption,
            thumb=thumbnail_path,
            progress=wrapped_progress
        )
        
        logger.info(f"Successfully uploaded document to chat {chat_id}")
        return message
    
    async def upload_audio(
        self,
        chat_id: int,
        audio_path: str,
        caption: str = "",
        duration: int = 0,
        performer: str = "",
        title: str = "",
        thumbnail_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Optional[Message]:
        """
        Upload an audio file using Pyrogram client.
        
        Args:
            chat_id: Target chat ID
            audio_path: Path to the audio file
            caption: Audio caption
            duration: Audio duration in seconds
            performer: Audio performer/artist
            title: Audio title
            thumbnail_path: Path to thumbnail image (optional)
            progress_callback: Callback function for upload progress
            progress_args: Additional arguments for progress callback
            
        Returns:
            Message object if successful, None otherwise
        """
        
        if not os.path.exists(audio_path):
            logger.error(f"Audio file not found: {audio_path}")
            return None
        
        if thumbnail_path and not os.path.exists(thumbnail_path):
            logger.warning(f"Thumbnail file not found: {thumbnail_path}")
            thumbnail_path = None
        
        try:
            return await pyrogram_manager.execute_with_client(
                self._upload_audio_with_client,
                chat_id=chat_id,
                audio_path=audio_path,
                caption=caption,
                duration=duration,
                performer=performer,
                title=title,
                thumbnail_path=thumbnail_path,
                progress_callback=progress_callback,
                progress_args=progress_args
            )
            
        except Exception as e:
            logger.error(f"Failed to upload audio {audio_path}: {e}")
            return None
    
    async def _upload_audio_with_client(
        self,
        client: Client,
        chat_id: int,
        audio_path: str,
        caption: str = "",
        duration: int = 0,
        performer: str = "",
        title: str = "",
        thumbnail_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None,
        progress_args: tuple = ()
    ) -> Message:
        """Internal method to upload audio with Pyrogram client"""
        
        # Prepare progress callback wrapper
        wrapped_progress = None
        if progress_callback:
            wrapped_progress = self._create_progress_wrapper(
                progress_callback, progress_args
            )
        
        # Upload the audio
        message = await client.send_audio(
            chat_id=chat_id,
            audio=audio_path,
            caption=caption,
            duration=duration,
            performer=performer,
            title=title,
            thumb=thumbnail_path,
            progress=wrapped_progress
        )
        
        logger.info(f"Successfully uploaded audio to chat {chat_id}")
        return message


# Global uploader instance
video_uploader = VideoUploader()
