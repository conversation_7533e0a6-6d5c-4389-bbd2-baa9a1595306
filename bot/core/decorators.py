from functools import wraps
from bot.utils.db import is_admin
import logging

logger = logging.getLogger(__name__)

def adminsOnly(func):
    @wraps(func)
    async def wrapper(event, *args, **kwargs):
        sender = await event.get_sender()
        sender_id = sender.id
        
        admin_status = await is_admin(sender_id)
        
        if admin_status:
            return await func(event, *args, **kwargs)
        else:
            await event.reply("You are not authorized to use this command.")
            return None
    
    return wrapper