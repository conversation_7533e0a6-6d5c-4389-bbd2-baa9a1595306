import asyncio
import logging
from typing import <PERSON><PERSON>
from pyrogram import C<PERSON>
from pyrogram.errors import <PERSON><PERSON><PERSON>
from bot.config import Config

logger = logging.getLogger(__name__)


class PyrogramClientManager:
    """
    Manages Pyrogram client lifecycle to avoid event loop collisions with Telethon.
    Uses a separate thread for Pyrogram operations when needed.
    """
    
    def __init__(self):
        self._client: Optional[Client] = None
        self._is_running = False
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> bool:
        """Initialize the Pyrogram client"""
        async with self._lock:
            if self._client is not None:
                logger.warning("Pyrogram client already initialized")
                return True
            
            try:
                self._client = Client(
                    name=Config.PYROGRAM_SESSION_NAME,
                    api_id=Config.API_ID,
                    api_hash=Config.API_HASH,
                    bot_token=Config.BOT_TOKEN,
                    workers=Config.PYROGRAM_WORKERS,
                    workdir=".",
                    no_updates=True  # Important: disable updates to avoid conflicts
                )
                
                logger.info("Pyrogram client initialized successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to initialize Pyrogram client: {e}")
                self._client = None
                return False
    
    async def start(self) -> bool:
        """Start the Pyrogram client"""
        async with self._lock:
            if self._client is None:
                logger.error("Pyrogram client not initialized")
                return False
            
            if self._is_running:
                logger.warning("Pyrogram client already running")
                return True
            
            try:
                await self._client.start()
                self._is_running = True
                logger.info("Pyrogram client started successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to start Pyrogram client: {e}")
                return False
    
    async def stop(self) -> bool:
        """Stop the Pyrogram client"""
        async with self._lock:
            if self._client is None or not self._is_running:
                return True
            
            try:
                await self._client.stop()
                self._is_running = False
                logger.info("Pyrogram client stopped successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to stop Pyrogram client: {e}")
                return False
    
    async def get_client(self) -> Optional[Client]:
        """Get the Pyrogram client instance"""
        async with self._lock:
            if self._client is None:
                logger.error("Pyrogram client not initialized")
                return None
            
            if not self._is_running:
                logger.error("Pyrogram client not running")
                return None
            
            return self._client
    
    async def is_ready(self) -> bool:
        """Check if the client is ready for use"""
        return self._client is not None and self._is_running
    
    async def execute_with_client(self, func, *args, **kwargs):
        """
        Execute a function with the Pyrogram client, handling common errors.
        This method ensures proper error handling and retry logic.
        """
        client = await self.get_client()
        if client is None:
            raise RuntimeError("Pyrogram client not available")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return await func(client, *args, **kwargs)
            
            except FloodWait as e:
                if attempt < max_retries - 1:
                    logger.warning(f"FloodWait error, waiting {e.value} seconds (attempt {attempt + 1})")
                    await asyncio.sleep(e.value)
                    continue
                else:
                    raise
            
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Error in Pyrogram operation (attempt {attempt + 1}): {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    raise


# Global instance
pyrogram_manager = PyrogramClientManager()


async def init_pyrogram_client() -> bool:
    """Initialize and start the global Pyrogram client"""
    success = await pyrogram_manager.initialize()
    if success:
        success = await pyrogram_manager.start()
    return success


async def stop_pyrogram_client() -> bool:
    """Stop the global Pyrogram client"""
    return await pyrogram_manager.stop()


async def get_pyrogram_client() -> Optional[Client]:
    """Get the global Pyrogram client instance"""
    return await pyrogram_manager.get_client()


async def is_pyrogram_ready() -> bool:
    """Check if the global Pyrogram client is ready"""
    return await pyrogram_manager.is_ready()
