from io import BytesIO
import logging
import os
from aiohttp import ClientSession

logger = logging.getLogger(__name__)

async def extract_userid(event):
    if event.is_reply:
        reply_msg = await event.get_reply_message()
        sender = await reply_msg.get_sender()
        return sender.id
    else:
        args = event.message.message.split()
        if len(args) >= 2:
            identifier = args[1]
            if identifier.isdigit():
                return int(identifier)
            else:
                try:
                    user = await event.client.get_entity(identifier)
                    return user.id
                except ValueError:
                    await event.reply("Invalid username or user ID.")
                    return None
        else:
            await event.reply("Please reply to a user's message or provide a username/user ID.")
            return None
        
async def download_file(url: str, filename: str, ext: str = None) -> tuple[BytesIO, str] | tuple[None, str]:
    buffer = BytesIO()
    try:
        async with ClientSession() as session, session.get(url) as resp:
            if resp.status != 200:
                return None, filename
            async for chunk in resp.content.iter_chunked(8192):
                buffer.write(chunk)
        buffer.seek(0)
        return buffer, f"{os.path.splitext(filename)[0]}.{ext}" if ext else filename
    except Exception as e:
        logger.error(f"Download error: {e}")
        return None, filename