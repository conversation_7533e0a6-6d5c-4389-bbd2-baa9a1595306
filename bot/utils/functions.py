async def extract_userid(event):
    if event.is_reply:
        reply_msg = await event.get_reply_message()
        sender = await reply_msg.get_sender()
        return sender.id
    else:
        args = event.message.message.split()
        if len(args) >= 2:
            identifier = args[1]
            if identifier.isdigit():
                return int(identifier)
            else:
                try:
                    user = await event.client.get_entity(identifier)
                    return user.id
                except ValueError:
                    await event.reply("Invalid username or user ID.")
                    return None
        else:
            await event.reply("Please reply to a user's message or provide a username/user ID.")
            return None