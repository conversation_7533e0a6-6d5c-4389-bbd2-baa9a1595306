from datetime import datetime
from bot import TIMEZON<PERSON>, db

admins = db.admins
users = db.users

async def add_admin(admin_id):
    if not await admins.find_one({"user_id": admin_id}):
        await admins.insert_one({"user_id": admin_id})
        return True
    return False

async def remove_admin(admin_id):
    if await admins.find_one({"user_id": admin_id}):
        await admins.delete_one({"user_id": admin_id})
        return True
    return False

async def is_admin(admin_id):
    if await admins.find_one({"user_id": admin_id}):
        return True
    return False

async def add_user(user_id):
    user_data = {
        "user_id": user_id,
        "created_at": datetime.now(TIMEZONE),
        "last_seen": datetime.now(TIMEZONE)
    }
    
    existing_user = await users.find_one({"user_id": user_id})
    if not existing_user:
        await users.insert_one(user_data)
    else:
        await users.update_one(
            {"user_id": user_id},
            {"$set": {"last_seen": datetime.now(TIMEZONE)}}
        )