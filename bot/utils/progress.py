import asyncio
import time
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class ProgressTracker:
    """
    Handles upload/download progress tracking and updates.
    Designed to work with both Telethon and Pyrogram.
    """
    
    def __init__(self, message, action_text: str = "Processing"):
        self.message = message
        self.action_text = action_text
        self.last_update_time = 0
        self.update_interval = 2  # Update every 2 seconds
        self.last_percentage = -1
    
    async def update_progress(self, current: int, total: int, *args):
        """
        Update progress with rate limiting to avoid flood limits.
        
        Args:
            current: Current bytes processed
            total: Total bytes to process
            *args: Additional arguments (ignored)
        """
        try:
            current_time = time.time()
            
            # Calculate percentage
            percentage = int((current / total) * 100) if total > 0 else 0
            
            # Rate limiting: only update if enough time has passed or percentage changed significantly
            if (current_time - self.last_update_time < self.update_interval and 
                abs(percentage - self.last_percentage) < 5):
                return
            
            # Format file size
            current_mb = current / (1024 * 1024)
            total_mb = total / (1024 * 1024)
            
            # Create progress bar
            progress_bar = self._create_progress_bar(percentage)
            
            # Update message
            progress_text = (
                f"{self.action_text}... {percentage}%\n"
                f"{progress_bar}\n"
                f"{current_mb:.1f} MB / {total_mb:.1f} MB"
            )
            
            # Try to edit the message
            try:
                await self.message.edit(progress_text)
                self.last_update_time = current_time
                self.last_percentage = percentage
            except Exception as edit_error:
                # If editing fails, log but don't raise (might be rate limited)
                logger.debug(f"Failed to update progress message: {edit_error}")
                
        except Exception as e:
            logger.error(f"Error in progress update: {e}")
    
    def _create_progress_bar(self, percentage: int, length: int = 20) -> str:
        """Create a visual progress bar"""
        filled = int(length * percentage / 100)
        bar = "█" * filled + "░" * (length - filled)
        return f"[{bar}]"


async def create_progress_callback(message, action_text: str = "Processing"):
    """
    Create a progress callback function for upload/download operations.
    
    Args:
        message: Telethon message object to update
        action_text: Text to display during progress
        
    Returns:
        Async callback function
    """
    tracker = ProgressTracker(message, action_text)
    return tracker.update_progress


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds: int) -> str:
    """
    Format duration in human readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes}m {seconds}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours}h {minutes}m {seconds}s"


class SimpleProgressCallback:
    """
    Simple progress callback that just logs progress without updating messages.
    Useful for debugging or when message updates are not needed.
    """
    
    def __init__(self, description: str = "Operation"):
        self.description = description
        self.last_percentage = -1
    
    async def __call__(self, current: int, total: int, *args):
        """Progress callback implementation"""
        try:
            percentage = int((current / total) * 100) if total > 0 else 0
            
            # Only log significant changes
            if abs(percentage - self.last_percentage) >= 10:
                current_mb = current / (1024 * 1024)
                total_mb = total / (1024 * 1024)
                logger.info(
                    f"{self.description}: {percentage}% "
                    f"({current_mb:.1f}/{total_mb:.1f} MB)"
                )
                self.last_percentage = percentage
                
        except Exception as e:
            logger.error(f"Error in simple progress callback: {e}")
