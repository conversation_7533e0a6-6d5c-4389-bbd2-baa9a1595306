from os import environ
from dotenv import load_dotenv

load_dotenv()

class Config:
    API_ID = int(environ.get("API_ID", ))
    API_HASH = environ.get("API_HASH", "")
    BOT_TOKEN = environ.get("BOT_TOKEN", "")
    MONGO_URL = environ.get("MONGO_URL", "")
    DB_NAME = environ.get("DB_NAME", "")
    LOG_GROUP_ID = environ.get("LOG_GROUP_ID", "")
    YTLOG_TOPIC_ID = environ.get("YTLOG_TOPIC_ID", "")
    FSUB_IDS = environ.get("FSUB_IDS", "")
    INVITE_LINK_EXPIRY_TIME = int(environ.get("INVITE_LINK_EXPIRY_TIME", "3600"))  # 1 hour default
    MAX_USERS_ALLOWED = int(environ.get("MAX_USERS_ALLOWED", "100"))  # 100 users default
    
    # Pyrogram specific settings
    PYROGRAM_SESSION_NAME = environ.get("PYROGRAM_SESSION_NAME", "pyrogram_bot")
    PYROGRAM_WORKERS = int(environ.get("PYROGRAM_WORKERS", "4"))
