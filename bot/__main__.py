import logging
import asyncio
from bot import Bo<PERSON>, BOT_USERNAME, BOT_ID
from bot.config import Config
from bot.core.pyrogram_client import init_pyrogram_client, stop_pyrogram_client
import importlib
from pathlib import Path

logger = logging.getLogger(__name__)

def load_plugins():
    plugin_dir = Path(__file__).parent / "plugins"

    if not plugin_dir.exists():
        logger.error(f"Plugins directory not found: {plugin_dir}")
        return

    for plugin_file in plugin_dir.glob("*.py"):
        if plugin_file.stem == "__init__":
            continue

        module_name = f"bot.plugins.{plugin_file.stem}"
        try:
            importlib.import_module(module_name)
            logger.info(f"Successfully loaded plugin: {plugin_file.stem}")
        except Exception as e:
            logger.error(f"Failed to load plugin {plugin_file.stem}: {str(e)}")

async def main():
    global BOT_USERNAME, BOT_ID

    load_plugins()

    try:
        # Start Telethon bot
        await Bot.start(bot_token=Config.BOT_TOKEN)
        logger.info("Telethon bot started!")

        # Initialize and start Pyrogram client
        logger.info("Initializing Pyrogram client...")
        pyrogram_success = await init_pyrogram_client()
        if pyrogram_success:
            logger.info("Pyrogram client initialized and started successfully!")
        else:
            logger.warning("Failed to initialize Pyrogram client. Video uploads may not work optimally.")

        # Get bot information
        me = await Bot.get_me()
        BOT_USERNAME = me.username
        BOT_ID = me.id
        logger.info(f"Bot info retrieved - Username: @{BOT_USERNAME}, ID: {BOT_ID}")

        # Run until disconnected
        await Bot.run_until_disconnected()

    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise
    finally:
        # Cleanup Pyrogram client
        logger.info("Stopping Pyrogram client...")
        await stop_pyrogram_client()
        logger.info("Cleanup completed.")

if __name__ == '__main__':
    try:
        logger.info("Starting bot...")
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("Bot stopped.")