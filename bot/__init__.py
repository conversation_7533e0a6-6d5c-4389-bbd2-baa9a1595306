import os
from telethon import TelegramClient
from motor.motor_asyncio import AsyncIOMotorClient as MongoClient
import logging
import pytz
from bot.config import Config

TIMEZONE = pytz.timezone('Asia/Kolkata')

os.makedirs("logs", exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/jarvis_saver_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

logger.info("Initializing MongoDB client")
mongo_client = MongoClient(Config.MONGO_URL)
db = mongo_client[Config.DB_NAME]

# Telethon client for main bot functionality
Bot = TelegramClient('Bot', api_id=Config.API_ID, api_hash=Config.API_HASH)

# Pyrogram client will be initialized separately to avoid event loop conflicts
PyroBot = None

BOT_USERNAME = None
BOT_ID = None