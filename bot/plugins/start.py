from telethon import events, But<PERSON>
from bot import Bo<PERSON>, BOT_USERNAME
from bot.plugins.f_sub import fsub
from bot.utils.db import add_user

@Bot.on(events.NewMessage(pattern='/start'))
@fsub
async def start(event):
    user = event.sender
    mention = f'<a href="tg://user?id={user.id}">{user.first_name}</a>'
    
    await add_user(user.id)

    msg = f"""👋 Hello {mention}

I can help you download high-quality videos from YouTube, Instagram, and other platforms.

Just send a video link, and I’ll fetch the video for you within seconds (without watermark).

<blockquote>Want to use me in a group? Tap below 👇</blockquote>"""

    buttons = [[Button.url("➕ Add to Group", f"https://t.me/{BOT_USERNAME}?startgroup=true")]]

    await event.reply(msg, buttons=buttons, parse_mode='html')
