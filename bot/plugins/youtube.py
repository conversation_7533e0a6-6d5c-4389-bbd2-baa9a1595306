import os
import logging
import yt_dlp
from telethon import events, <PERSON><PERSON>, types
from bot import Bot
from bot.plugins.f_sub import fsub
from bot.core.video_uploader import video_uploader
from bot.core.pyrogram_client import is_pyrogram_ready
from bot.utils.functions import download_file
from bot.utils.progress import create_progress_callback, format_file_size

logger = logging.getLogger(__name__)

# Quality presets mapping
QUALITY_PRESETS = {
    'best': {'label': 'Best Quality', 'format': 'best'},
    '1080p': {'label': '1080p', 'format': 'bestvideo[height<=1080]+bestaudio/best[height<=1080]'},
    '720p': {'label': '720p', 'format': 'bestvideo[height<=720]+bestaudio/best[height<=720]'},
    '480p': {'label': '480p', 'format': 'bestvideo[height<=480]+bestaudio/best[height<=480]'},
    '360p': {'label': '360p', 'format': 'bestvideo[height<=360]+bestaudio/best[height<=360]'},
    'audio': {'label': 'Audio Only', 'format': 'bestaudio/best'}
}

@Bot.on(events.NewMessage(incoming=True, pattern=r'^(http(s)?:\/\/)?((w){3}.)?youtu(be|.be)?(\.com)?\/.+'))
@fsub
async def ytlink_handler(event):
    youtube_link = event.text.strip()
    
    try:
        # Quick metadata extraction for thumbnail
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'cookiefile': 'cookies.txt'
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(youtube_link, download=False)
            title = info.get('title', 'YouTube Video')
            thumbnail = info.get('thumbnail', None)
        
        # Create buttons in 2 columns
        buttons = []
        qualities = list(QUALITY_PRESETS.items())
        for i in range(0, len(qualities), 2):
            row = []
            for quality, preset in qualities[i:i+2]:
                row.append(Button.inline(preset['label'], data=f'download|{quality}|{youtube_link}'))
            buttons.append(row)
        
        if thumbnail:
            await event.reply(
                f"**{title}**\n\nSelect download quality:",
                file=thumbnail,
                buttons=buttons
            )
        else:
            await event.reply(
                f"**{title}**\n\nSelect download quality:",
                buttons=buttons
            )
            
    except Exception as e:
        logging.error(f"Error processing YouTube link: {e}")
        await event.reply("❌ Couldn't fetch video information. Please try again.")

@Bot.on(events.CallbackQuery(pattern=r'^download\|'))
async def download_handler(event):
    data = event.data.decode('utf-8')
    _, quality, youtube_link = data.split('|', 2)

    if quality not in QUALITY_PRESETS:
        quality = 'best'

    format_spec = QUALITY_PRESETS[quality]['format']
    temp_file = f"temp_{event.query.user_id}.mp4"
    temp_thumbnail = f"temp_thumb_{event.query.user_id}.jpg"

    try:
        await event.edit("⬇️ Downloading video...")

        ydl_opts = {
            'format': format_spec,
            'outtmpl': temp_file,
            'merge_output_format': 'mp4',
            'quiet': True,
            'no_warnings': True,
            'cookiefile': 'cookies.txt',
            'extract_flat': False,
            'writethumbnail': True,
            'writeinfojson': False
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(youtube_link, download=True)
            duration = info.get('duration', 0)
            title = info.get('title', 'YouTube Video')
            width = info.get('width', 0)
            height = info.get('height', 0)
            thumb_url = info.get('thumbnail', None)

            if not os.path.exists(temp_file):
                raise Exception("Downloaded file not found")

            # Check if Pyrogram is available for better upload performance
            use_pyrogram = await is_pyrogram_ready()

            if use_pyrogram and quality != 'audio':
                # Use Pyrogram for video uploads (better performance and features)
                await event.edit("⬆️ Uploading video via Pyrogram...")

                # Create progress callback
                progress_callback = await create_progress_callback(event, "⬆️ Uploading")

                # Find thumbnail file (yt-dlp creates it with various extensions)
                thumb = (await download_file(thumb_url, "thumbnail", "png"))[0] if thumb_url else None

                # Upload using Pyrogram
                result = await video_uploader.upload_video(
                    chat_id=event.chat_id,
                    video_path=temp_file,
                    caption=title,
                    thumbnail_path=thumb,
                    duration=duration,
                    width=width,
                    height=height,
                    supports_streaming=True,
                    progress_callback=progress_callback
                )

                if result:
                    await event.edit("✅ Upload complete!")
                else:
                    raise Exception("Pyrogram upload failed")

            else:
                # Fallback to Telethon upload
                await event.edit("⬆️ Uploading video...")

                # Prepare attributes
                attrs = [
                    types.DocumentAttributeVideo(
                        duration=duration,
                        w=width,
                        h=height,
                        supports_streaming=True
                    )
                ]

                # Find thumbnail file
                thumbnail_path = None
                for ext in ['.jpg', '.jpeg', '.png', '.webp']:
                    thumb_path = temp_file.rsplit('.', 1)[0] + ext
                    if os.path.exists(thumb_path):
                        thumbnail_path = thumb_path
                        break

                # Send file with thumbnail if available
                if thumbnail_path and quality != 'audio':
                    await event.client.send_file(
                        event.chat_id,
                        temp_file,
                        caption=title,
                        thumb=thumbnail_path,
                        attributes=attrs
                    )
                else:
                    await event.client.send_file(
                        event.chat_id,
                        temp_file,
                        caption=title,
                        attributes=attrs
                    )

                await event.edit("✅ Download complete!")

    except Exception as e:
        logger.error(f"Error downloading video: {e}")
        await event.edit("❌ Download failed. Please try again.")
    finally:
        # Cleanup all temporary files
        for file_path in [temp_file, temp_thumbnail]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup {file_path}: {cleanup_error}")

        # Cleanup thumbnail files created by yt-dlp
        for ext in ['.jpg', '.jpeg', '.png', '.webp']:
            thumb_path = temp_file.rsplit('.', 1)[0] + ext
            if os.path.exists(thumb_path):
                try:
                    os.remove(thumb_path)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup {thumb_path}: {cleanup_error}")