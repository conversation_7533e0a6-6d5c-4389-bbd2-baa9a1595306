import os
import logging
import yt_dlp
from telethon import events, But<PERSON>, types
from bot import Bot
from bot.plugins.f_sub import fsub

logger = logging.getLogger(__name__)

# Quality presets mapping
QUALITY_PRESETS = {
    'best': {'label': 'Best Quality', 'format': 'best'},
    '1080p': {'label': '1080p', 'format': 'bestvideo[height<=1080]+bestaudio/best[height<=1080]'},
    '720p': {'label': '720p', 'format': 'bestvideo[height<=720]+bestaudio/best[height<=720]'},
    '480p': {'label': '480p', 'format': 'bestvideo[height<=480]+bestaudio/best[height<=480]'},
    '360p': {'label': '360p', 'format': 'bestvideo[height<=360]+bestaudio/best[height<=360]'},
    'audio': {'label': 'Audio Only', 'format': 'bestaudio/best'}
}

@Bot.on(events.NewMessage(incoming=True, pattern=r'^(http(s)?:\/\/)?((w){3}.)?youtu(be|.be)?(\.com)?\/.+'))
@fsub
async def ytlink_handler(event):
    youtube_link = event.text.strip()
    
    try:
        # Quick metadata extraction for thumbnail
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'cookiefile': 'cookies.txt'
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(youtube_link, download=False)
            title = info.get('title', 'YouTube Video')
            thumbnail = info.get('thumbnail', None)
        
        # Create buttons in 2 columns
        buttons = []
        qualities = list(QUALITY_PRESETS.items())
        for i in range(0, len(qualities), 2):
            row = []
            for quality, preset in qualities[i:i+2]:
                row.append(Button.inline(preset['label'], data=f'download|{quality}|{youtube_link}'))
            buttons.append(row)
        
        if thumbnail:
            await event.reply(
                f"**{title}**\n\nSelect download quality:",
                file=thumbnail,
                buttons=buttons
            )
        else:
            await event.reply(
                f"**{title}**\n\nSelect download quality:",
                buttons=buttons
            )
            
    except Exception as e:
        logging.error(f"Error processing YouTube link: {e}")
        await event.reply("❌ Couldn't fetch video information. Please try again.")

@Bot.on(events.CallbackQuery(pattern=r'^download\|'))
async def download_handler(event):
    data = event.data.decode('utf-8')
    _, quality, youtube_link = data.split('|', 2)
    
    if quality not in QUALITY_PRESETS:
        quality = 'best'
    
    format_spec = QUALITY_PRESETS[quality]['format']
    temp_file = f"temp_{event.query.user_id}.mp4"
    
    try:
        await event.edit("⬇️ Downloading video...")
        
        ydl_opts = {
            'format': format_spec,
            'outtmpl': temp_file,
            'merge_output_format': 'mp4',
            'quiet': True,
            'no_warnings': True,
            'cookiefile': 'cookies.txt',
            'extract_flat': False
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(youtube_link, download=True)
            duration = info.get('duration', 0)
            title = info.get('title', 'YouTube Video')
            thumbnail = info.get('thumbnail', None)
            
            if not os.path.exists(temp_file):
                raise Exception("Downloaded file not found")
            
            await event.edit("⬆️ Uploading video...")
            
            # Prepare attributes
            attrs = [
                types.DocumentAttributeVideo(
                    duration=duration,
                    w=info.get('width', 0),
                    h=info.get('height', 0),
                    supports_streaming=True
                )
            ]
            
            # Send file with thumbnail if available
            if thumbnail and quality != 'audio':
                await event.client.send_file(
                    event.chat_id,
                    temp_file,
                    caption=title,
                    thumb=thumbnail,
                    attributes=attrs
                )
            else:
                await event.client.send_file(
                    event.chat_id,
                    temp_file,
                    caption=title,
                    attributes=attrs
                )
            
            await event.edit("✅ Download complete!")
            
    except Exception as e:
        logging.error(f"Error downloading video: {e}")
        await event.edit("❌ Download failed. Please try again.")
    finally:
        if os.path.exists(temp_file):
            os.remove(temp_file)