from functools import wraps
from bot.config import Config
from bot import Bo<PERSON>
from telethon import Button, events
from telethon.errors import UserNotParticipantError, ChatAdminRequiredError, ChannelPrivateError
from telethon.tl.functions.messages import ExportChatInviteRequest
from telethon.tl.types import Channel, Chat
import logging
import asyncio
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

async def get_invite_link(chat_id):
    try:
        expiry_timestamp = int((datetime.now() + timedelta(seconds=Config.INVITE_LINK_EXPIRY_TIME)).timestamp())
        
        invite_link = await <PERSON><PERSON>(ExportChatInviteRequest(
            peer=chat_id,
            expire_date=expiry_timestamp,
            usage_limit=Config.MAX_USERS_ALLOWED
        ))
        return invite_link.link
    except ChatAdminRequiredError:
        try:
            chat = await Bot.get_entity(chat_id)
            if isinstance(chat, (<PERSON>, Chat)) and hasattr(chat, 'username') and chat.username:
                return f"https://t.me/{chat.username}"
            logger.error(f"Bot is not admin in chat {chat_id} and no public username available")
            return None
        except Exception as e:
            logger.error(f"Error getting chat info for {chat_id}: {str(e)}")
            return None
    except Exception as e:
        logger.error(f"Error creating invite link for {chat_id}: {str(e)}")
        return None


async def check_user_subscription(user_id, chat_id):
    try:
        participant = await Bot.get_permissions(chat_id, user_id)
        return participant is not None
    except UserNotParticipantError:
        return False
    except ChannelPrivateError:
        return False
    except Exception as e:
        logger.error(f"Error checking subscription for user {user_id} in chat {chat_id}: {str(e)}")
        return False


async def get_unsubscribed_chats(user_id):
    if not Config.FSUB_IDS or Config.FSUB_IDS.strip() == "":
        return []
    
    try:
        fsub_ids = [int(chat_id.strip()) for chat_id in Config.FSUB_IDS.split(',') if chat_id.strip()]
    except ValueError as e:
        logger.error(f"Invalid FSUB_IDS format: {str(e)}")
        return []
    
    if not fsub_ids:
        return []
    
    subscription_tasks = [check_user_subscription(user_id, chat_id) for chat_id in fsub_ids]
    subscription_results = await asyncio.gather(*subscription_tasks, return_exceptions=True)
    
    unsubscribed_chats = []
    for i, result in enumerate(subscription_results):
        if isinstance(result, Exception):
            logger.error(f"Error checking subscription for chat {fsub_ids[i]}: {str(result)}")
            unsubscribed_chats.append(fsub_ids[i])
        elif not result:
            unsubscribed_chats.append(fsub_ids[i])
    
    return unsubscribed_chats


async def create_subscription_buttons(unsubscribed_chats):
    if not unsubscribed_chats:
        return []
    
    buttons = []
    temp_row = []
    
    invite_link_tasks = [get_invite_link(chat_id) for chat_id in unsubscribed_chats]
    invite_links = await asyncio.gather(*invite_link_tasks, return_exceptions=True)
    
    for i, chat_id in enumerate(unsubscribed_chats):
        invite_link = invite_links[i]
        
        if isinstance(invite_link, Exception):
            logger.error(f"Error getting invite link for chat {chat_id}: {str(invite_link)}")
            continue
            
        if not invite_link:
            continue
            
        try:
            chat = await Bot.get_entity(chat_id)
            if isinstance(chat, Channel):
                button_text = f"✨ Join Channel"
            else:
                button_text = f"⚡️ Join Group"
                
            button = Button.url(button_text, invite_link)
            temp_row.append(button)
            
            # Create rows of 2 buttons each
            if len(temp_row) == 2:
                buttons.append(temp_row)
                temp_row = []
                
        except Exception as e:
            logger.error(f"Error creating button for chat {chat_id}: {str(e)}")
            continue
    
    # Add remaining button if any
    if temp_row:
        buttons.append(temp_row)
    
    # Add retry button
    if buttons:
        buttons.append([Button.inline("↺ Retry", "retry_fsub")])
    
    return buttons


def fsub(func):
    @wraps(func)
    async def wrapper(event, *args, **kwargs):
        user_id = event.sender_id
        
        unsubscribed_chats = await get_unsubscribed_chats(user_id)
        
        if not unsubscribed_chats:
            return await func(event, *args, **kwargs)
        
        buttons = await create_subscription_buttons(unsubscribed_chats)
        
        if not buttons:
            logger.warning("No valid subscription buttons could be created, proceeding with function")
            return await func(event, *args, **kwargs)
        
        text = f"Please join all chats below to use this bot\n\n"
        text += "<blockquote>After joining, click <b>↺ Retry</b> to continue.</blockquote>"
        
        await event.reply(text, buttons=buttons, parse_mode='html')
        return
        
    return wrapper


@Bot.on(events.CallbackQuery(data=b'retry_fsub'))
async def retry_fsub_handler(event):
    user_id = event.sender_id
    
    unsubscribed_chats = await get_unsubscribed_chats(user_id)
    
    if not unsubscribed_chats:
        await event.answer("✅ Subscription verified! You can now use the bot.", alert=False)
        await event.delete()
        return
    
    await event.answer(f"❌ Please join all chats first", alert=True)
    
    updated_buttons = await create_subscription_buttons(unsubscribed_chats)
    if updated_buttons:
        updated_text = f"Please join all chats below to use this bot\n\n"
        updated_text += "<blockquote>After joining, click <b>↺ Retry</b> to continue.</blockquote>"
        
        await event.edit(updated_text, buttons=updated_buttons, parse_mode='html')