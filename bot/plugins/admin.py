from telethon import events
from bot import Bot
import logging
from bot.core.decorators import adminsOnly
from bot.utils.db import add_admin, remove_admin
from bot.utils.functions import extract_userid

logger = logging.getLogger(__name__)

@Bot.on(events.NewMessage(pattern='/addsudo'))
@adminsOnly
async def add_sudo(event):
    user_id = await extract_userid(event)
    if not user_id:
        return
    
    result = await add_admin(user_id)
    if result:
        await event.reply(f"User {user_id} has been added as a sudo user.")
    else:
        await event.reply(f"User {user_id} is already a sudo user.")
        
@Bot.on(events.NewMessage(pattern='/removesudo'))
@adminsOnly
async def remove_sudo(event):
    user_id = await extract_userid(event)
    if not user_id:
        return
    
    result = await remove_admin(user_id)
    if result:
        await event.reply(f"User {user_id} has been removed from sudo users.")
    else:
        await event.reply(f"User {user_id} is not a sudo user.")